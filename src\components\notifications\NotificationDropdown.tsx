/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
import React, { useState } from 'react';
import {
  Popover,
  Box,
  Text,
  Group,
  Divider,
  Center,
  Loader,
  Stack,
  ActionIcon,
  Badge,
  Tooltip,
} from '@mantine/core';
import { IconBellRinging, IconCheck } from '@tabler/icons-react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import {
  getAdminNotificationsQuery,
  getUnreadCountQuery,
  markAsReadMutation,
  markAllAsReadMutation,
} from '../../requests/admin-notifications';
import NotificationBell from './NotificationBell';

interface NotificationDropdownProps {
  maxHeight?: number;
  maxItems?: number;
}

export default function NotificationDropdown({
  maxHeight = 400,
  maxItems = 10,
}: NotificationDropdownProps) {
  const [opened, setOpened] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();
  const { data: session } = useSession();

  // Fetch admin notifications
  const {
    data: notificationData,
    isLoading,
  } = useQuery({
    ...getAdminNotificationsQuery({
      pagination: { page: 1, limit: maxItems || 5 },
      filters: { read: false }, // Only show unread in dropdown
    }),
    enabled: opened && !!session?.user,
  });

  // Fetch unread count
  const { data: unreadData } = useQuery(getUnreadCountQuery());

  const notifications = notificationData?.data?.notifications || [];
  const unreadCount = unreadData?.data?.unreadCount || 0;

  // Mark as read mutation
  const markAsReadMut = useMutation({
    ...markAsReadMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
    },
  });

  // Mark all as read mutation
  const markAllAsReadMut = useMutation({
    ...markAllAsReadMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsReadMut.mutateAsync({ id: notificationId });
    } catch (error) {
      //
    }
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    try {
      await markAllAsReadMut.mutateAsync();
      notifications.show({
        title: 'Success',
        message: 'All notifications marked as read',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to mark as read',
        color: 'red',
      });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleNotificationClick = (notification: any) => {
    // Mark as read when clicked
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }

    // Close dropdown
    setOpened(false);

    // Navigate based on notification type and content
    if (notification.shipmentId) {
      // If notification has shipment_id, navigate to shipment details
      router.push(`/shipments?id=${notification.shipmentId}`);
    } else {
      // Navigate to admin notifications page
      router.push('/admin-notifications');
    }
  };

  // COMMENTED OUT: Disable refetch when dropdown opens
  // useEffect(() => {
  //   if (opened) {
  //     refetch();
  //   }
  // }, [opened, refetch]);

  return (
    <Popover
      width={380}
      position="bottom-end"
      withArrow
      shadow="lg"
      opened={opened}
      onChange={setOpened}
      trapFocus={false}
      closeOnEscape
      closeOnClickOutside
      withinPortal
      keepMounted={false}
      zIndex={10500}
      offset={8}
      middlewares={{
        flip: true,
        shift: { padding: 8 },
        size: true,
      }}
    >
      <Popover.Target>
        <Box>
          <NotificationBell onClick={() => setOpened((o) => !o)} />
        </Box>
      </Popover.Target>

      <Popover.Dropdown p={0} data-notification-dropdown>
        <Box>
          {/* Header */}
          <Group justify="space-between" p="md" pb="sm">
            <Group gap="xs">
              <IconBellRinging size="1.1rem" />
              <Text fw={600} size="md">
                Notifications
              </Text>
              {unreadCount > 0 && (
                <Badge size="sm" color="blue" variant="light">
                  {unreadCount}
                </Badge>
              )}
            </Group>

            {/* Always show mark all as read button for debugging */}
            <Tooltip label="Mark all as read" position="top">
              <ActionIcon
                variant="subtle"
                size="md"
                onClick={handleMarkAllAsRead}
                loading={markAllAsReadMut.isPending}
                disabled={unreadCount === 0}
                style={{
                  opacity: unreadCount === 0 ? 0.5 : 1,
                  padding: '4px',
                }}
              >
                <IconCheck size="1.2rem" />
              </ActionIcon>
            </Tooltip>
          </Group>

          <Divider />

          {/* Content */}
          <Box style={{ maxHeight, minHeight: 200 }}>
            {(() => {
              if (isLoading) {
                return (
                  <Center py="xl">
                    <Stack align="center" gap="sm">
                      <Loader size="sm" />
                      <Text size="sm" c="dimmed">
                        Loading notifications...
                      </Text>
                    </Stack>
                  </Center>
                );
              }
              if (notifications.length === 0) {
                return (
                  <Center py="xl">
                    <Stack align="center" gap="sm">
                      <IconBellRinging size="2rem" color="gray" />
                      <Text size="sm" c="dimmed">
                        No new notifications
                      </Text>
                    </Stack>
                  </Center>
                );
              }

              return (
                <Stack gap={0}>
                  {notifications.map((notification: any) => (
                    <Box
                      key={notification.id}
                      p="md"
                      style={{
                        cursor: 'pointer',
                        borderBottom: '1px solid var(--mantine-color-gray-2)',
                        backgroundColor: notification.read ? 'transparent' : 'var(--mantine-color-blue-0)',
                      }}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <Group gap="sm" align="flex-start">
                        <Box
                          style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: notification.read ? 'transparent' : 'var(--mantine-color-blue-6)',
                            marginTop: 6,
                          }}
                        />
                        <Box style={{ flex: 1 }}>
                          <Text size="sm" fw={notification.read ? 400 : 600} lineClamp={2}>
                            {notification.title}
                          </Text>
                          <Text size="xs" c="dimmed" lineClamp={2} mt={2}>
                            {notification.message}
                          </Text>
                          <Group gap="xs" mt={4}>
                            <Badge size="xs" color={notification.priority === 'URGENT' ? 'red' : notification.priority === 'HIGH' ? 'orange' : 'blue'}>
                              {notification.priority}
                            </Badge>
                            <Text size="xs" c="dimmed">
                              {new Date(notification.createdAt).toLocaleDateString()}
                            </Text>
                          </Group>
                        </Box>
                      </Group>
                    </Box>
                  ))}

                  {notifications.length > 0 && (
                    <Box p="md" style={{ borderTop: '1px solid var(--mantine-color-gray-2)' }}>
                      <Text
                        size="sm"
                        c="blue"
                        style={{ cursor: 'pointer', textAlign: 'center' }}
                        onClick={() => {
                          setOpened(false);
                          router.push('/admin-notifications');
                        }}
                      >
                        View all notifications
                      </Text>
                    </Box>
                  )}
                </Stack>
              );
            })()}
          </Box>
        </Box>
      </Popover.Dropdown>
    </Popover>
  );
}
